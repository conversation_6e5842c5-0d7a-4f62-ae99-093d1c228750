{"name": "brandify", "version": "1.0.0", "description": "AI-powered brand identity generator - Build your brand in 30 seconds", "main": "server/index.js", "scripts": {"dev": "concurrently \"npm run server:dev\" \"npm run client:dev\"", "server:dev": "nodemon server/index.js", "client:dev": "live-server public --port=3001", "start": "node server/index.js", "build": "npm run client:build", "client:build": "echo 'Building client assets...'", "test": "jest", "lint": "eslint .", "format": "prettier --write ."}, "keywords": ["branding", "ai", "logo-generator", "saas", "design", "brand-identity"], "author": "Brandify Team", "license": "MIT", "dependencies": {"@google/generative-ai": "^0.24.1", "archiver": "^6.0.1", "axios": "^1.6.2", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.0.3", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.7", "sharp": "^0.33.1", "stripe": "^14.9.0", "uuid": "^9.0.1"}, "devDependencies": {"@types/node": "^20.10.5", "concurrently": "^8.2.2", "eslint": "^8.55.0", "jest": "^29.7.0", "live-server": "^1.2.2", "nodemon": "^3.0.2", "prettier": "^3.1.1"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}