const { GoogleGenerativeAI } = require('@google/generative-ai');

class AIService {
    constructor() {
        if (!process.env.GOOGLE_AI_API_KEY) {
            throw new Error('GOOGLE_AI_API_KEY is required');
        }
        
        this.genAI = new GoogleGenerativeAI(process.env.GOOGLE_AI_API_KEY);
        this.model = this.genAI.getGenerativeModel({ model: "gemini-pro" });
    }

    async generateBrandIdentity(description) {
        try {
            const prompt = this.createBrandPrompt(description);
            const result = await this.model.generateContent(prompt);
            const response = await result.response;
            const text = response.text();
            
            return this.parseBrandResponse(text);
        } catch (error) {
            console.error('Error generating brand identity:', error);
            throw new Error('Failed to generate brand identity');
        }
    }

    createBrandPrompt(description) {
        return `
You are a professional brand designer and strategist. Create a comprehensive brand identity based on this description: "${description}"

Please provide a detailed brand identity in the following JSON format:

{
  "brandName": "A suggested brand name based on the description",
  "tagline": "A catchy tagline or slogan",
  "brandPersonality": ["3-5 personality traits"],
  "colorPalette": {
    "primary": "#hexcode",
    "secondary": "#hexcode", 
    "accent": "#hexcode",
    "neutral": "#hexcode"
  },
  "typography": {
    "heading": "Font family name for headings",
    "body": "Font family name for body text",
    "style": "modern/classic/playful/elegant/etc"
  },
  "logoStyle": {
    "type": "wordmark/symbol/combination",
    "description": "Detailed description of the logo concept",
    "elements": ["key visual elements to include"]
  },
  "brandVoice": {
    "tone": "professional/friendly/authoritative/etc",
    "style": "conversational/formal/casual/etc",
    "characteristics": ["2-3 voice characteristics"]
  },
  "targetAudience": "Description of the target audience",
  "brandValues": ["3-5 core brand values"],
  "usageGuidelines": {
    "doUse": ["2-3 usage recommendations"],
    "dontUse": ["2-3 usage restrictions"]
  }
}

Make sure the response is valid JSON only, no additional text or formatting.
Base all decisions on the brand description provided and ensure consistency across all elements.
Choose colors that work well together and reflect the brand personality.
Select fonts that are web-safe and match the brand style.
`;
    }

    parseBrandResponse(text) {
        try {
            // Clean the response to extract JSON
            const jsonMatch = text.match(/\{[\s\S]*\}/);
            if (!jsonMatch) {
                throw new Error('No JSON found in response');
            }
            
            const jsonText = jsonMatch[0];
            const brandData = JSON.parse(jsonText);
            
            // Validate required fields
            this.validateBrandData(brandData);
            
            return brandData;
        } catch (error) {
            console.error('Error parsing brand response:', error);
            // Return fallback brand data
            return this.getFallbackBrandData();
        }
    }

    validateBrandData(brandData) {
        const requiredFields = ['brandName', 'colorPalette', 'typography', 'logoStyle'];
        for (const field of requiredFields) {
            if (!brandData[field]) {
                throw new Error(`Missing required field: ${field}`);
            }
        }

        // Validate color palette
        const requiredColors = ['primary', 'secondary', 'accent'];
        for (const color of requiredColors) {
            if (!brandData.colorPalette[color]) {
                throw new Error(`Missing required color: ${color}`);
            }
        }

        // Validate typography
        if (!brandData.typography.heading || !brandData.typography.body) {
            throw new Error('Missing required typography fields');
        }
    }

    getFallbackBrandData() {
        return {
            brandName: "Your Brand",
            tagline: "Professional. Reliable. Innovative.",
            brandPersonality: ["Professional", "Modern", "Trustworthy"],
            colorPalette: {
                primary: "#2563eb",
                secondary: "#64748b",
                accent: "#f59e0b",
                neutral: "#f8fafc"
            },
            typography: {
                heading: "Inter",
                body: "Inter",
                style: "modern"
            },
            logoStyle: {
                type: "combination",
                description: "A clean, modern logo combining text and a simple geometric symbol",
                elements: ["Typography", "Geometric shape", "Clean lines"]
            },
            brandVoice: {
                tone: "professional",
                style: "conversational",
                characteristics: ["Clear", "Confident", "Approachable"]
            },
            targetAudience: "Modern professionals and businesses",
            brandValues: ["Quality", "Innovation", "Reliability", "Transparency"],
            usageGuidelines: {
                doUse: ["Maintain consistent spacing", "Use approved color combinations"],
                dontUse: ["Distort the logo", "Use unapproved colors"]
            }
        };
    }

    async generateLogoSVG(logoStyle, colorPalette, brandName) {
        try {
            const prompt = `
Create a simple SVG logo based on these specifications:
- Brand name: "${brandName}"
- Logo type: ${logoStyle.type}
- Style: ${logoStyle.description}
- Primary color: ${colorPalette.primary}
- Secondary color: ${colorPalette.secondary}

Generate clean, minimal SVG code for a logo that is:
- Scalable and professional
- Uses the specified colors
- Incorporates the brand name if it's a wordmark or combination logo
- Is suitable for web and print use

Return only the SVG code, no additional text.
`;

            const result = await this.model.generateContent(prompt);
            const response = await result.response;
            const svgCode = response.text();
            
            // Extract SVG from response
            const svgMatch = svgCode.match(/<svg[\s\S]*<\/svg>/i);
            return svgMatch ? svgMatch[0] : this.getFallbackSVG(brandName, colorPalette.primary);
            
        } catch (error) {
            console.error('Error generating logo SVG:', error);
            return this.getFallbackSVG(brandName, colorPalette.primary);
        }
    }

    getFallbackSVG(brandName, primaryColor) {
        const initials = brandName.split(' ').map(word => word[0]).join('').toUpperCase().slice(0, 2);
        
        return `
<svg width="200" height="80" viewBox="0 0 200 80" xmlns="http://www.w3.org/2000/svg">
  <rect width="60" height="60" x="10" y="10" rx="12" fill="${primaryColor}"/>
  <text x="40" y="45" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="white" text-anchor="middle">${initials}</text>
  <text x="85" y="35" font-family="Arial, sans-serif" font-size="20" font-weight="600" fill="${primaryColor}">${brandName}</text>
  <text x="85" y="55" font-family="Arial, sans-serif" font-size="12" fill="#666">Professional Brand Identity</text>
</svg>`.trim();
    }

    generateMockupData(brandData) {
        return {
            businessCard: {
                frontSide: {
                    backgroundColor: brandData.colorPalette.neutral,
                    primaryColor: brandData.colorPalette.primary,
                    secondaryColor: brandData.colorPalette.secondary
                },
                backSide: {
                    backgroundColor: brandData.colorPalette.primary,
                    textColor: 'white'
                }
            },
            letterhead: {
                headerColor: brandData.colorPalette.primary,
                accentColor: brandData.colorPalette.accent
            },
            website: {
                headerBackground: brandData.colorPalette.primary,
                buttonColor: brandData.colorPalette.accent,
                textColor: brandData.colorPalette.secondary
            },
            socialMedia: {
                profileBackground: brandData.colorPalette.primary,
                accentColor: brandData.colorPalette.accent
            }
        };
    }
}

module.exports = AIService;
