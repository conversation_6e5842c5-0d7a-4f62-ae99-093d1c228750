// Brand Generator functionality
class BrandGenerator {
    constructor() {
        this.currentBrand = null;
        this.isGenerating = false;
        this.init();
    }

    init() {
        this.setupEventListeners();
    }

    setupEventListeners() {
        const generateBtn = document.getElementById('generateBtn');
        const brandInput = document.getElementById('brandDescription');
        const regenerateBtn = document.getElementById('regenerateBtn');
        const downloadBtn = document.getElementById('downloadBtn');

        if (generateBtn) {
            generateBtn.addEventListener('click', () => this.generateBrand());
        }

        if (brandInput) {
            brandInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.generateBrand();
                }
            });

            // Auto-resize input based on content
            brandInput.addEventListener('input', (e) => {
                this.validateInput(e.target.value);
            });
        }

        if (regenerateBtn) {
            regenerateBtn.addEventListener('click', () => this.generateBrand());
        }

        if (downloadBtn) {
            downloadBtn.addEventListener('click', () => this.downloadBrandKit());
        }
    }

    validateInput(value) {
        const generateBtn = document.getElementById('generateBtn');
        const isValid = value.trim().length >= 3;
        
        if (generateBtn) {
            generateBtn.disabled = !isValid || this.isGenerating;
        }

        return isValid;
    }

    async generateBrand() {
        const brandInput = document.getElementById('brandDescription');
        const description = brandInput?.value?.trim();

        if (!description || description.length < 3) {
            this.showError('Please enter a brand description (at least 3 characters)');
            return;
        }

        if (this.isGenerating) {
            return;
        }

        this.setGeneratingState(true);

        try {
            const response = await fetch('/api/brand/generate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ description })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            
            if (data.success && data.brand) {
                this.currentBrand = data.brand;
                this.displayBrandResult(data.brand);
                this.showSuccess('Brand generated successfully!');
            } else {
                throw new Error(data.message || 'Failed to generate brand');
            }

        } catch (error) {
            console.error('Error generating brand:', error);
            this.showError('Failed to generate brand. Please try again.');
        } finally {
            this.setGeneratingState(false);
        }
    }

    setGeneratingState(isGenerating) {
        this.isGenerating = isGenerating;
        const generateBtn = document.getElementById('generateBtn');
        const btnText = generateBtn?.querySelector('.btn-text');
        const btnLoading = generateBtn?.querySelector('.btn-loading');

        if (generateBtn) {
            generateBtn.disabled = isGenerating;
        }

        if (btnText && btnLoading) {
            if (isGenerating) {
                btnText.style.display = 'none';
                btnLoading.style.display = 'flex';
            } else {
                btnText.style.display = 'inline';
                btnLoading.style.display = 'none';
            }
        }
    }

    displayBrandResult(brand) {
        const resultSection = document.getElementById('brandResult');
        const resultDescription = document.getElementById('resultDescription');

        if (resultDescription) {
            const description = brand.brandName ?
                `${brand.brandName} - "${brand.description}"` :
                `Generated for: "${brand.description}"`;
            resultDescription.textContent = description;

            // Add tagline if available
            if (brand.tagline) {
                resultDescription.innerHTML = `
                    <strong>${brand.brandName}</strong><br>
                    <em>"${brand.tagline}"</em><br>
                    <small>Generated for: "${brand.description}"</small>
                `;
            }
        }

        // Display logo
        this.displayLogo(brand.logo);

        // Display colors
        this.displayColors(brand.colors);

        // Display typography
        this.displayTypography(brand.typography);

        // Display brand details
        this.displayBrandDetails(brand);

        // Display mockups
        this.displayMockups(brand.mockups);

        // Show the result section
        if (resultSection) {
            resultSection.style.display = 'block';
            resultSection.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    }

    displayLogo(logo) {
        const logoDisplay = document.getElementById('logoDisplay');
        if (!logoDisplay || !logo) return;

        if (logo.svg) {
            logoDisplay.innerHTML = `
                <div style="text-align: center;">
                    <div style="margin-bottom: 16px;">
                        ${logo.svg}
                    </div>
                    ${logo.style ? `
                        <div style="margin-top: 16px; padding: 12px; background: var(--gray-50); border-radius: 8px;">
                            <h4 style="margin: 0 0 8px 0; font-size: 14px; color: var(--gray-700);">Logo Style</h4>
                            <p style="margin: 0; font-size: 12px; color: var(--gray-600);">
                                <strong>Type:</strong> ${logo.style.type}<br>
                                <strong>Description:</strong> ${logo.style.description}
                            </p>
                        </div>
                    ` : ''}
                </div>
            `;
        } else {
            logoDisplay.innerHTML = `
                <div style="text-align: center;">
                    <div style="font-size: 48px; font-weight: bold; color: var(--primary-color); margin-bottom: 16px;">
                        LOGO
                    </div>
                    <p style="color: var(--gray-600); margin: 0;">
                        Logo generation in progress...<br>
                        This will show your AI-generated logo variations.
                    </p>
                </div>
            `;
        }
    }

    displayColors(colors) {
        const colorPalette = document.getElementById('colorPalette');
        if (!colorPalette || !colors) return;

        const colorEntries = Object.entries(colors);
        colorPalette.innerHTML = colorEntries.map(([name, color]) => `
            <div class="color-swatch">
                <div class="color-circle" style="background-color: ${color};"></div>
                <div class="color-code">${color}</div>
                <div style="font-size: 12px; color: var(--gray-500); text-transform: capitalize;">
                    ${name}
                </div>
            </div>
        `).join('');
    }

    displayTypography(typography) {
        const typographyDisplay = document.getElementById('typographyDisplay');
        if (!typographyDisplay || !typography) return;

        typographyDisplay.innerHTML = `
            <div class="font-sample">
                <div class="font-label">Heading Font - ${typography.heading}</div>
                <div class="font-preview" style="font-family: ${typography.heading}, sans-serif;">
                    Your Brand Name
                </div>
                <div style="font-family: ${typography.heading}, sans-serif; margin-top: 8px; font-size: 14px;">
                    The quick brown fox jumps over the lazy dog
                </div>
            </div>
            <div class="font-sample">
                <div class="font-label">Body Font - ${typography.body}</div>
                <div style="font-family: ${typography.body}, sans-serif; font-size: 16px; line-height: 1.5;">
                    This is how your body text will look. It's clean, readable, and professional.
                </div>
                <div style="font-family: ${typography.body}, sans-serif; margin-top: 8px; font-size: 14px; color: var(--gray-600);">
                    The quick brown fox jumps over the lazy dog
                </div>
            </div>
            ${typography.style ? `
                <div style="margin-top: 16px; padding: 12px; background: var(--gray-50); border-radius: 8px;">
                    <div style="font-size: 12px; color: var(--gray-600);">
                        <strong>Typography Style:</strong> ${typography.style}
                    </div>
                </div>
            ` : ''}
        `;
    }

    displayBrandDetails(brand) {
        // Add brand personality, voice, and values to the showcase
        const showcaseContainer = document.querySelector('.brand-showcase');
        if (!showcaseContainer) return;

        // Check if brand details section already exists
        let brandDetailsSection = document.getElementById('brandDetailsSection');
        if (!brandDetailsSection) {
            brandDetailsSection = document.createElement('div');
            brandDetailsSection.id = 'brandDetailsSection';
            brandDetailsSection.className = 'showcase-section';
            brandDetailsSection.innerHTML = '<h3>Brand Details</h3><div id="brandDetailsContent"></div>';
            showcaseContainer.appendChild(brandDetailsSection);
        }

        const brandDetailsContent = document.getElementById('brandDetailsContent');
        if (!brandDetailsContent) return;

        let detailsHTML = '';

        if (brand.brandPersonality && brand.brandPersonality.length > 0) {
            detailsHTML += `
                <div style="margin-bottom: 20px;">
                    <h4 style="margin: 0 0 8px 0; font-size: 14px; color: var(--gray-700);">Brand Personality</h4>
                    <div style="display: flex; gap: 8px; flex-wrap: wrap;">
                        ${brand.brandPersonality.map(trait => `
                            <span style="background: var(--primary-color); color: white; padding: 4px 12px; border-radius: 16px; font-size: 12px;">
                                ${trait}
                            </span>
                        `).join('')}
                    </div>
                </div>
            `;
        }

        if (brand.brandValues && brand.brandValues.length > 0) {
            detailsHTML += `
                <div style="margin-bottom: 20px;">
                    <h4 style="margin: 0 0 8px 0; font-size: 14px; color: var(--gray-700);">Brand Values</h4>
                    <ul style="margin: 0; padding-left: 16px; font-size: 14px; color: var(--gray-600);">
                        ${brand.brandValues.map(value => `<li>${value}</li>`).join('')}
                    </ul>
                </div>
            `;
        }

        if (brand.targetAudience) {
            detailsHTML += `
                <div style="margin-bottom: 20px;">
                    <h4 style="margin: 0 0 8px 0; font-size: 14px; color: var(--gray-700);">Target Audience</h4>
                    <p style="margin: 0; font-size: 14px; color: var(--gray-600);">${brand.targetAudience}</p>
                </div>
            `;
        }

        if (brand.brandVoice) {
            detailsHTML += `
                <div style="margin-bottom: 20px;">
                    <h4 style="margin: 0 0 8px 0; font-size: 14px; color: var(--gray-700);">Brand Voice</h4>
                    <p style="margin: 0; font-size: 14px; color: var(--gray-600);">
                        <strong>Tone:</strong> ${brand.brandVoice.tone}<br>
                        <strong>Style:</strong> ${brand.brandVoice.style}
                        ${brand.brandVoice.characteristics ? `<br><strong>Characteristics:</strong> ${brand.brandVoice.characteristics.join(', ')}` : ''}
                    </p>
                </div>
            `;
        }

        brandDetailsContent.innerHTML = detailsHTML || '<p style="color: var(--gray-500); font-style: italic;">Brand details will appear here</p>';
    }

    displayMockups(mockups) {
        const mockupsGrid = document.getElementById('mockupsGrid');
        if (!mockupsGrid || !mockups) return;

        mockupsGrid.innerHTML = mockups.map(mockup => `
            <div class="mockup-item">
                <div style="text-align: center; color: var(--gray-500);">
                    <div style="font-size: 24px; margin-bottom: 8px;">📱</div>
                    <div style="font-size: 14px;">Mockup Preview</div>
                    <div style="font-size: 12px;">Coming Soon</div>
                </div>
            </div>
        `).join('');
    }

    async downloadBrandKit() {
        if (!this.currentBrand) {
            this.showError('No brand to download. Please generate a brand first.');
            return;
        }

        try {
            const response = await fetch(`/api/brand/${this.currentBrand.id}/download`, {
                method: 'POST'
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            // For now, show a placeholder message
            this.showSuccess('Download functionality coming soon! Your brand kit will include logos, colors, fonts, and mockups.');
            
            // TODO: Implement actual file download
            // const blob = await response.blob();
            // const url = window.URL.createObjectURL(blob);
            // const a = document.createElement('a');
            // a.href = url;
            // a.download = `brandify-kit-${this.currentBrand.id}.zip`;
            // a.click();
            // window.URL.revokeObjectURL(url);

        } catch (error) {
            console.error('Error downloading brand kit:', error);
            this.showError('Failed to download brand kit. Please try again.');
        }
    }

    showSuccess(message) {
        if (window.brandifyApp) {
            window.brandifyApp.showNotification(message, 'success');
        } else {
            alert(message);
        }
    }

    showError(message) {
        if (window.brandifyApp) {
            window.brandifyApp.showNotification(message, 'error');
        } else {
            alert(message);
        }
    }
}

// Initialize the brand generator when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.brandGenerator = new BrandGenerator();
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = BrandGenerator;
}
